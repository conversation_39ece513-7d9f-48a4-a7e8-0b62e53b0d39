#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能模糊匹配功能
"""

import sys
from pathlib import Path
import pandas as pd

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.excel_generator import ExcelGenerator


def test_fuzzy_matching():
    """测试智能模糊匹配功能"""
    print("=" * 60)
    print("测试智能模糊匹配功能")
    print("=" * 60)
    
    # 创建测试实例
    generator = ExcelGenerator()
    
    # 读取每日工作总表
    try:
        daily_work_df = pd.read_excel("每日工作总表.xlsx")
        generator.daily_work_df = daily_work_df
        print(f"✅ 成功读取每日工作总表，共 {len(daily_work_df)} 行数据")
    except Exception as e:
        print(f"❌ 读取每日工作总表失败: {e}")
        return
    
    # 测试用例
    test_cases = [
        ("100306507884460", "运营表格中的15位编码"),
        ("100301790052218", "另一个15位编码"),
        ("123456789012345", "不存在的编码"),
    ]
    
    print(f"\n🔍 开始测试模糊匹配:")
    print("-" * 40)
    
    for operation_code, description in test_cases:
        print(f"\n测试编码: {operation_code} ({description})")

        # 先检查每日工作总表中的相关编码
        daily_codes = daily_work_df['运营编码'].astype(str).str.strip().unique()

        # 查找包含相似数字序列的编码
        operation_digits = generator.extract_digits_from_code(operation_code)
        print(f"运营编码数字部分: {operation_digits}")

        similar_codes = []
        for daily_code in daily_codes:
            daily_digits = generator.extract_digits_from_code(str(daily_code))
            if daily_digits and operation_digits:
                # 检查是否有包含关系
                if (len(operation_digits) > len(daily_digits) and daily_digits in operation_digits) or \
                   (len(daily_digits) > len(operation_digits) and operation_digits in daily_digits):
                    similar_codes.append((daily_code, daily_digits))

        print(f"找到相似编码: {len(similar_codes)} 个")
        for code, digits in similar_codes[:5]:
            print(f"  {code} -> {digits}")

        # 测试模糊匹配
        fuzzy_matches = generator.find_fuzzy_operation_code_matches(operation_code)

        print(f"模糊匹配结果: {len(fuzzy_matches)} 个匹配")
        for match in fuzzy_matches:
            print(f"  - {match}")

        # 如果有匹配，显示对应的数据
        if fuzzy_matches:
            for match_code in list(fuzzy_matches)[:2]:  # 只显示前2个匹配
                matched_data = daily_work_df[daily_work_df['运营编码'].astype(str).str.strip() == match_code]
                print(f"  匹配编码 '{match_code}' 的数据: {len(matched_data)} 条")
                if len(matched_data) > 0:
                    sample_row = matched_data.iloc[0]
                    print(f"    示例: 操作人={sample_row.get('操作人', 'N/A')}, 手机名称={sample_row.get('手机名称', 'N/A')}")
    
    print(f"\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)


if __name__ == "__main__":
    print("开始测试智能模糊匹配功能...")
    
    try:
        test_fuzzy_matching()
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试运营编码匹配过程
"""

import sys
from pathlib import Path
import pandas as pd

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.file_manager import FileManager
from core.excel_generator import ExcelGenerator


def debug_matching_process():
    """调试完整的匹配过程"""
    print("=" * 80)
    print("调试运营编码匹配过程")
    print("=" * 80)
    
    target_code = "10030650788460"
    print(f"目标运营编码: {target_code}")
    
    # 第一步：检查运营表格文件读取
    print(f"\n" + "=" * 50)
    print("第一步：检查运营表格文件读取")
    print("=" * 50)
    
    file_manager = FileManager()
    
    # 查找运营表格文件
    operation_files = list(Path(".").glob("*.xlsx"))
    operation_files = [f for f in operation_files if "每日工作总表" not in f.name and "客服每日工作总表" not in f.name]
    
    print(f"找到的运营表格文件: {[f.name for f in operation_files]}")
    
    operation_data = {}
    for op_file in operation_files:
        try:
            # 读取前两行检查运营编码
            header_df = pd.read_excel(op_file, header=None, nrows=2)
            if len(header_df.columns) >= 2:
                raw_operation_code = str(header_df.iloc[0, 1]).strip()
                cleaned_code = file_manager.clean_operation_code(raw_operation_code)
                
                print(f"\n文件: {op_file.name}")
                print(f"  原始B1内容: '{raw_operation_code}'")
                print(f"  清理后编码: '{cleaned_code}'")
                print(f"  是否匹配目标: {cleaned_code == target_code}")
                
                if cleaned_code == target_code:
                    # 读取完整文件
                    full_df = pd.read_excel(op_file, header=None)
                    data_rows = full_df.iloc[2:].values.tolist()  # 从第3行开始
                    operation_data[cleaned_code] = {
                        'file_name': op_file.name,
                        'data_rows': data_rows
                    }
                    print(f"  ✅ 找到匹配文件，数据行数: {len(data_rows)}")
        except Exception as e:
            print(f"  ❌ 读取文件 {op_file.name} 失败: {e}")
    
    if target_code not in operation_data:
        print(f"\n❌ 未找到运营编码 '{target_code}' 对应的运营表格文件")
        return
    
    # 第二步：检查每日工作总表读取
    print(f"\n" + "=" * 50)
    print("第二步：检查每日工作总表读取")
    print("=" * 50)
    
    try:
        daily_work_df = pd.read_excel("每日工作总表.xlsx")
        print(f"✅ 成功读取每日工作总表，共 {len(daily_work_df)} 行数据")
        
        # 检查运营编码列
        if '运营编码' in daily_work_df.columns:
            operation_codes = daily_work_df['运营编码'].astype(str).str.strip()
            target_matches = operation_codes[operation_codes == target_code]
            print(f"✅ 找到目标编码 '{target_code}' 的数据: {len(target_matches)} 条")
            
            if len(target_matches) > 0:
                # 显示匹配的数据
                matched_data = daily_work_df[operation_codes == target_code]
                print(f"\n匹配的数据详情:")
                for i, (idx, row) in enumerate(matched_data.head(3).iterrows()):
                    print(f"  [{i+1}] 行 {idx+1}:")
                    print(f"      操作人: {row.get('操作人', 'N/A')}")
                    print(f"      手机名称: {row.get('手机名称', 'N/A')}")
                    print(f"      运营编码: {row.get('运营编码', 'N/A')}")
                    print(f"      日期: {row.get('日期', 'N/A')}")
        else:
            print(f"❌ 每日工作总表中未找到'运营编码'列")
            print(f"   可用列: {list(daily_work_df.columns)}")
            return
            
    except Exception as e:
        print(f"❌ 读取每日工作总表失败: {e}")
        return
    
    # 第三步：测试匹配逻辑
    print(f"\n" + "=" * 50)
    print("第三步：测试匹配逻辑")
    print("=" * 50)
    
    generator = ExcelGenerator()
    
    # 测试 get_daily_work_codes_for_operation
    daily_work_codes = generator.get_daily_work_codes_for_operation(target_code, operation_data)
    print(f"get_daily_work_codes_for_operation 结果: {daily_work_codes}")
    
    # 测试纯数字编码提取
    extracted_digits = generator.extract_digits_from_code(target_code)
    print(f"提取的纯数字编码: {extracted_digits}")
    
    # 第四步：模拟完整的筛选过程
    print(f"\n" + "=" * 50)
    print("第四步：模拟完整的筛选过程")
    print("=" * 50)
    
    # 添加纯数字编码列
    daily_work_df['运营编码_纯数字'] = daily_work_df['运营编码'].astype(str).apply(generator.extract_digits_from_code)
    
    # 获取唯一编码集合
    unique_daily_codes = set(daily_work_df['运营编码'].astype(str).str.strip())
    unique_daily_digit_codes = set(daily_work_df['运营编码_纯数字'].dropna().astype(str))
    
    print(f"每日工作总表中的运营编码数量: {len(unique_daily_codes)}")
    print(f"每日工作总表中的纯数字编码数量: {len(unique_daily_digit_codes)}")
    
    # 检查目标编码是否在集合中
    print(f"目标编码在运营编码集合中: {target_code in unique_daily_codes}")
    print(f"目标编码在纯数字编码集合中: {extracted_digits in unique_daily_digit_codes}")
    
    # 获取要匹配的编码
    daily_work_digit_codes = {generator.extract_digits_from_code(code) for code in daily_work_codes}
    daily_work_digit_codes.discard(None)
    
    print(f"要匹配的运营编码: {daily_work_codes}")
    print(f"要匹配的纯数字编码: {daily_work_digit_codes}")
    
    # 执行筛选
    staff_daily_data = daily_work_df[
        (daily_work_df['运营编码'].isin(daily_work_codes)) | 
        (daily_work_df['运营编码_纯数字'].isin(daily_work_digit_codes))
    ].copy()
    
    print(f"\n筛选结果:")
    print(f"筛选到的数据量: {len(staff_daily_data)}")
    
    if len(staff_daily_data) > 0:
        print(f"✅ 成功筛选到数据！")
        print(f"前3条数据:")
        for i, (idx, row) in enumerate(staff_daily_data.head(3).iterrows()):
            print(f"  [{i+1}] {row.get('操作人', 'N/A')} - {row.get('手机名称', 'N/A')} - {row.get('运营编码', 'N/A')}")
    else:
        print(f"❌ 未筛选到任何数据")
        
        # 详细分析为什么没有数据
        print(f"\n详细分析:")
        print(f"1. 直接运营编码匹配:")
        direct_match = daily_work_df[daily_work_df['运营编码'].isin(daily_work_codes)]
        print(f"   结果: {len(direct_match)} 条")
        
        print(f"2. 纯数字编码匹配:")
        digit_match = daily_work_df[daily_work_df['运营编码_纯数字'].isin(daily_work_digit_codes)]
        print(f"   结果: {len(digit_match)} 条")
        
        # 检查数据类型问题
        print(f"\n数据类型检查:")
        print(f"daily_work_codes 类型: {[type(code) for code in daily_work_codes]}")
        print(f"daily_work_digit_codes 类型: {[type(code) for code in daily_work_digit_codes]}")
        print(f"每日工作表运营编码类型: {daily_work_df['运营编码'].dtype}")
        print(f"每日工作表纯数字编码类型: {daily_work_df['运营编码_纯数字'].dtype}")


if __name__ == "__main__":
    print("开始调试运营编码匹配过程...")
    
    try:
        debug_matching_process()
        
    except Exception as e:
        print(f"调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
